#!/usr/bin/env python3
"""
Flask Application for PDF Medical Document Search System
Provides REST API endpoints with same functionality as main_app.py
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import os
import sys
import traceback
import logging
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Import the same modules as main_app.py
from pdf_embedder import PDFEmbeddingPipeline
from search_system import MedicalSearchSystem

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('flask_app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global variables
search_system = None
pipeline = None

def initialize_systems():
    """Initialize the search system and pipeline"""
    global search_system, pipeline
    logger.info("Initializing Flask app systems...")
    try:
        # Get embedder type from environment variable
        embedder_type = os.getenv("EMBEDDER_TYPE", "nomic")
        logger.info(f"Using embedder type: {embedder_type}")

        search_system = MedicalSearchSystem(embedder_type=embedder_type)
        pipeline = PDFEmbeddingPipeline(embedder_type=embedder_type)
        logger.info("Flask app systems initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing systems: {e}", exc_info=True)
        return False

# HTML template for the web interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Document Search System</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; font-size: 1.2em; opacity: 0.9; }
        .section { background: white; padding: 25px; margin: 20px 0; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .section h3 { margin-top: 0; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .button-group { display: flex; gap: 15px; margin: 20px 0; flex-wrap: wrap; }
        .btn { padding: 12px 24px; font-size: 16px; border: none; border-radius: 8px; cursor: pointer; transition: all 0.3s; text-decoration: none; display: inline-block; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a6fd8; transform: translateY(-2px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #218838; transform: translateY(-2px); }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; transform: translateY(-2px); }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; transform: translateY(-2px); }
        .search-box { margin: 20px 0; }
        .search-box input { width: 70%; padding: 15px; font-size: 16px; border: 2px solid #ddd; border-radius: 8px; }
        .search-box input:focus { border-color: #667eea; outline: none; }
        .search-box button { padding: 15px 30px; font-size: 16px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; margin-left: 10px; }
        .search-box button:hover { background: #5a6fd8; }
        .results { margin-top: 30px; }
        .result-item { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #667eea; }
        .result-header { font-weight: bold; color: #333; margin-bottom: 8px; font-size: 1.1em; }
        .result-meta { color: #666; font-size: 14px; margin-bottom: 12px; }
        .result-content { line-height: 1.6; color: #444; }
        .loading { text-align: center; color: #667eea; font-size: 18px; padding: 40px; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #dc3545; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #28a745; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #17a2b8; }
        .stats { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; }
        .stats h3 { margin-top: 0; }
        .api-docs { background: #e9ecef; padding: 25px; border-radius: 12px; margin-top: 30px; }
        .endpoint { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #28a745; }
        .endpoint h4 { margin-top: 0; color: #333; }
        .endpoint code { background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-family: 'Courier New', monospace; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 Medical Document Search System</h1>
        <p>Advanced PDF processing and AI-powered medical document search</p>
    </div>

    <div class="stats" id="stats">
        <h3><span class="status-indicator status-offline" id="statusIndicator"></span>📊 System Status</h3>
        <p id="statusText">Loading system information...</p>
    </div>

    <div class="section">
        <h3>🔧 System Management</h3>
        <div class="button-group">
            <button class="btn btn-primary" onclick="setupDatabase()">🚀 Setup Database</button>
            <button class="btn btn-warning" onclick="testSinglePDF()">🧪 Test Single PDF</button>
            <button class="btn btn-info" onclick="getDatabaseInfo()">📊 Database Info</button>
            <button class="btn btn-success" onclick="loadSystemStatus()">🔄 Refresh Status</button>
        </div>
        <div id="managementResults"></div>
    </div>

    <div class="section">
        <h3>🔍 Search Documents</h3>
        <div class="search-box">
            <input type="text" id="searchQuery" placeholder="Enter your medical question..." onkeypress="handleKeyPress(event)">
            <button onclick="performSearch()">Search</button>
        </div>
        <div class="results" id="searchResults"></div>
    </div>

    <div class="api-docs">
        <h3>📚 API Documentation</h3>

        <div class="endpoint">
            <h4>POST /api/setup</h4>
            <p>Setup database and process all PDFs in the diseases folder</p>
            <p><strong>Body:</strong> <code>{"folder": "diseases"}</code> (optional)</p>
        </div>

        <div class="endpoint">
            <h4>POST /api/test</h4>
            <p>Test processing with a single PDF file</p>
            <p><strong>Body:</strong> <code>{"folder": "diseases"}</code> (optional)</p>
        </div>

        <div class="endpoint">
            <h4>GET /api/info</h4>
            <p>Get detailed database information and statistics</p>
        </div>

        <div class="endpoint">
            <h4>POST /api/search</h4>
            <p>Search documents and get AI-powered answers</p>
            <p><strong>Body:</strong> <code>{"query": "medical question", "top_k": 10}</code></p>
        </div>

        <div class="endpoint">
            <h4>GET /api/status</h4>
            <p>Get system health status</p>
        </div>
    </div>

    <script>
        // Load system status on page load
        window.onload = function() {
            loadSystemStatus();
        };

        function loadSystemStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.getElementById('statusIndicator');
                    const statusText = document.getElementById('statusText');

                    if (data.success) {
                        indicator.className = 'status-indicator status-online';
                        statusText.innerHTML = `
                            <strong>Status:</strong> ${data.status} |
                            <strong>Search System:</strong> ${data.search_system_ready ? 'Ready' : 'Not Ready'} |
                            <strong>Pipeline:</strong> ${data.pipeline_ready ? 'Ready' : 'Not Ready'}<br>
                            <strong>Last Updated:</strong> ${new Date().toLocaleString()}
                        `;
                    } else {
                        indicator.className = 'status-indicator status-offline';
                        statusText.innerHTML = `<strong>Status:</strong> Error - ${data.error}`;
                    }
                })
                .catch(error => {
                    const indicator = document.getElementById('statusIndicator');
                    const statusText = document.getElementById('statusText');
                    indicator.className = 'status-indicator status-offline';
                    statusText.innerHTML = '<strong>Status:</strong> Connection Error';
                });
        }

        function setupDatabase() {
            const resultsDiv = document.getElementById('managementResults');
            resultsDiv.innerHTML = '<div class="loading">🔧 Setting up database and processing PDFs...</div>';

            fetch('/api/setup', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultsDiv.innerHTML = `<div class="success">✅ ${data.message}</div>`;
                    loadSystemStatus(); // Refresh status
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function testSinglePDF() {
            const resultsDiv = document.getElementById('managementResults');
            resultsDiv.innerHTML = '<div class="loading">🧪 Testing single PDF processing...</div>';

            fetch('/api/test', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultsDiv.innerHTML = `<div class="success">✅ ${data.message}<br>📄 Tested file: ${data.filename}</div>`;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function getDatabaseInfo() {
            const resultsDiv = document.getElementById('managementResults');
            resultsDiv.innerHTML = '<div class="loading">📊 Loading database information...</div>';

            fetch('/api/info')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `<div class="info">
                            <h4>📊 Database Information</h4>
                            <p><strong>Total document chunks:</strong> ${data.total_documents}</p>
                            <p><strong>Unique files:</strong> ${data.unique_files}</p>
                            <h5>Files in database:</h5>
                            <ul>`;

                        data.files.forEach(file => {
                            html += `<li>${file.filename} - ${file.chunks} chunks</li>`;
                        });

                        html += '</ul></div>';
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                });
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        function performSearch() {
            const query = document.getElementById('searchQuery').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }

            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div class="loading">⏳ Searching documents and generating response...</div>';

            fetch('/api/search', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    query: query,
                    top_k: 10
                })
            })
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data, query);
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function displaySearchResults(data, query) {
            const resultsDiv = document.getElementById('searchResults');

            if (!data.success) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${data.error}</div>`;
                return;
            }

            let html = `<h4>🔍 Search Results for "${query}"</h4>`;
            html += `<p>📋 Found ${data.documents_found} relevant documents</p>`;

            if (data.answer) {
                html += `
                    <div class="result-item" style="border-left-color: #28a745;">
                        <div class="result-header">🤖 AI Response</div>
                        <div class="result-content">${data.answer.replace(/\\n/g, '<br>')}</div>
                    </div>
                `;
            }

            if (data.sources && data.sources.length > 0) {
                html += `<h5>📚 Sources used:</h5>`;
                data.sources.forEach((source, index) => {
                    // Handle NaN or invalid similarity scores
                    let relevance = 'N/A';
                    if (source.similarity_score !== null && source.similarity_score !== undefined && !isNaN(source.similarity_score)) {
                        relevance = (source.similarity_score * 100).toFixed(1) + '%';
                    }

                    html += `
                        <div class="result-item">
                            <div class="result-header">${index + 1}. ${source.filename}</div>
                            <div class="result-meta">Relevance: ${relevance}</div>
                        </div>
                    `;
                });
            }

            if (data.context) {
                const showContext = confirm('📄 Show raw document context?');
                if (showContext) {
                    const contextPreview = data.context.length > 2000 ?
                        data.context.substring(0, 2000) + '...' : data.context;
                    html += `
                        <div class="result-item" style="border-left-color: #ffc107;">
                            <div class="result-header">📄 Raw Context</div>
                            <div class="result-content">${contextPreview.replace(/\\n/g, '<br>')}</div>
                        </div>
                    `;
                }
            }

            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Serve the web interface"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get system status - equivalent to health check"""
    logger.debug("Status endpoint called")
    try:
        status_data = {
            'success': True,
            'status': 'operational',
            'search_system_ready': search_system is not None,
            'pipeline_ready': pipeline is not None,
            'timestamp': datetime.now().isoformat()
        }
        logger.debug(f"Status response: {status_data}")
        return jsonify(status_data)
    except Exception as e:
        logger.error(f"Error in status endpoint: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/setup', methods=['POST'])
def setup_database():
    """Setup database and process PDFs - equivalent to main_app.py --setup"""
    logger.info("Setup endpoint called")
    try:
        if not pipeline:
            logger.error("Pipeline not initialized for setup")
            return jsonify({
                'success': False,
                'error': 'Pipeline not initialized'
            }), 500

        # Get folder from request or use default
        data = request.get_json() or {}
        folder = data.get('folder', 'diseases')

        logger.info(f"Setting up database and processing PDFs from {folder}...")

        # Check if folder exists
        if not Path(folder).exists():
            logger.error(f"Folder {folder} not found")
            return jsonify({
                'success': False,
                'error': f'{folder} folder not found!'
            }), 400

        # Process PDF folder
        pipeline.process_pdf_folder(folder)

        response_data = {
            'success': True,
            'message': f'Setup completed successfully! Processed PDFs from {folder} folder.',
            'folder': folder,
            'timestamp': datetime.now().isoformat()
        }
        logger.info(f"Setup completed successfully for folder: {folder}")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error in setup endpoint: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc() if app.debug else None
        }), 500

@app.route('/api/test', methods=['POST'])
def test_single_pdf():
    """Test processing a single PDF - equivalent to main_app.py --test"""
    try:
        if not pipeline:
            return jsonify({
                'success': False,
                'error': 'Pipeline not initialized'
            }), 500

        # Get folder from request or use default
        data = request.get_json() or {}
        folder = data.get('folder', 'diseases')
        
        print("🧪 Testing single PDF processing...")
        
        # Find first PDF in folder
        diseases_folder = Path(folder)
        if not diseases_folder.exists():
            return jsonify({
                'success': False,
                'error': f'{folder} folder not found'
            }), 400
            
        pdf_files = list(diseases_folder.glob("*.pdf"))
        
        if not pdf_files:
            return jsonify({
                'success': False,
                'error': f'No PDF files found in {folder} folder'
            }), 400
            
        test_pdf = pdf_files[0]
        print(f"📄 Testing with: {test_pdf.name}")
        
        # Create tables and process single PDF
        pipeline.db_manager.create_tables()
        pipeline.process_single_pdf(str(test_pdf))
        
        return jsonify({
            'success': True,
            'message': 'Test completed successfully!',
            'filename': test_pdf.name,
            'folder': folder,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc() if app.debug else None
        }), 500

@app.route('/api/info', methods=['GET'])
def show_database_info():
    """Show database information - equivalent to main_app.py --info"""
    try:
        if not search_system:
            return jsonify({
                'success': False,
                'error': 'Search system not initialized'
            }), 500

        stats = search_system.get_database_stats()
        
        if "error" in stats:
            return jsonify({
                'success': False,
                'error': stats['error']
            }), 500
            
        return jsonify({
            'success': True,
            'total_documents': stats['total_documents'],
            'unique_files': stats['unique_files'],
            'files': stats['files'],
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc() if app.debug else None
        }), 500

@app.route('/api/search', methods=['POST'])
def search_and_answer():
    """Search documents and get AI answer - equivalent to main_app.py interactive search"""
    logger.info("Search endpoint called")
    try:
        if not search_system:
            logger.error("Search system not initialized for search")
            return jsonify({
                'success': False,
                'error': 'Search system not initialized'
            }), 500

        data = request.get_json()
        if not data:
            logger.warning("No JSON data provided to search endpoint")
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400

        query = data.get('query', '').strip()
        top_k = data.get('top_k', 10)

        logger.info(f"Search request - Query: '{query[:100]}...', top_k: {top_k}")

        if not query:
            logger.warning("Empty query provided to search endpoint")
            return jsonify({
                'success': False,
                'error': 'Query parameter is required'
            }), 400

        # Check if database has documents
        logger.debug("Checking database stats before search")
        stats = search_system.get_database_stats()
        if "error" in stats or stats.get("total_documents", 0) == 0:
            logger.warning("No documents found in database for search")
            return jsonify({
                'success': False,
                'error': 'No documents found in database. Please run setup first.'
            }), 400

        logger.info(f"Database has {stats.get('total_documents', 0)} documents, proceeding with search")

        # Search and get answer
        result = search_system.search_and_answer(query, top_k=top_k)

        response_data = {
            'success': True,
            'query': query,
            'documents_found': result['documents_found'],
            'answer': result['answer'],
            'sources': result['sources'],
            'context': result.get('context', ''),
            'timestamp': datetime.now().isoformat()
        }

        logger.info(f"Search completed successfully - found {result['documents_found']} documents, generated {len(result['answer'])} character response")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error in search endpoint: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc() if app.debug else None
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found',
        'available_endpoints': [
            'GET /',
            'GET /api/status',
            'POST /api/setup',
            'POST /api/test',
            'GET /api/info',
            'POST /api/search'
        ]
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': 'Please check the server logs for more details'
    }), 500

if __name__ == '__main__':
    logger.info("🚀 Starting Medical Document Search System API...")

    # Initialize systems with retry logic
    max_init_retries = 3
    initialization_successful = False

    for attempt in range(max_init_retries):
        logger.info(f"🔄 Initializing systems (attempt {attempt + 1}/{max_init_retries})...")
        if initialize_systems():
            logger.info("✅ Systems initialized successfully")
            initialization_successful = True
            break
        else:
            logger.warning(f"❌ Initialization attempt {attempt + 1} failed")
            if attempt < max_init_retries - 1:
                import time
                logger.info("⏳ Waiting 5 seconds before retry...")
                time.sleep(5)

    if not initialization_successful:
        logger.warning("⚠️  Starting API without full system initialization - some endpoints may not work")
        logger.warning("🔧 Please check your database connection and configuration")

    # Get configuration
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    logger.info(f"🌐 Starting Flask server on http://{host}:{port}")
    logger.info(f"📚 Web interface available at http://{host}:{port}")
    logger.info(f"🔍 API endpoints available at http://{host}:{port}/api/")
    logger.info(f"🌍 Environment: {os.getenv('FLASK_ENV', 'development')}")
    logger.info(f"🐛 Debug mode: {debug}")

    # Start the Flask app
    app.run(host=host, port=port, debug=debug)
