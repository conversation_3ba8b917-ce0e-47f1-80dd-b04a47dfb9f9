services:
  - type: web
    name: medical-document-search-api
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: python flask_app.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.8.10
      - key: DATABASE_URL
        value: postgres://postgres.cbopcqzonczkcvhjujvh:<EMAIL>:6543/postgres?sslmode=require
      - key: DB_NAME
        value: postgres
      - key: DB_USER
        value: postgres
      - key: DB_PASS
        value: sM0dwrPoUMzW8ecX
      - key: DB_HOST
        value: aws-0-us-east-1.pooler.supabase.com
      - key: DB_PORT
        value: 6543
      - key: DB_TABLE_NAME
        value: documents
      - key: GROQ_API_KEY
        value: ********************************************************
      - key: XAI_API_KEY
        value: ********************************************************
      - key: OLLAMA_BASE_URL
        value: http://localhost:11434
      - key: EMBEDDING_MODEL
        value: nomic-ai/nomic-embed-text-v1
      - key: FLASK_HOST
        value: 0.0.0.0
      - key: FLASK_PORT
        value: 10000
      - key: FLASK_DEBUG
        value: False
      - key: FLASK_ENV
        value: production
