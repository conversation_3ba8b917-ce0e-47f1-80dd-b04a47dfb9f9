import requests
import numpy as np

def get_embedding(text):
    url = "http://localhost:11434/api/embeddings"  # Adjust if needed
    data = {
        "model": "nomic-embed-text",  # or whatever name you use locally
        "prompt": text
    }

    response = requests.post(url, json=data)
    response.raise_for_status()

    embedding = response.json().get("embedding")
    if embedding is None:
        raise ValueError("No embedding returned")

    return np.array(embedding)


def cosine_similarity(vec1, vec2):
    return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))


def main():
    print("Enter Text 1:")
    text1 = input().strip()

    print("Enter Text 2:")
    text2 = input().strip()

    print("Generating embeddings using Nomic...")

    try:
        emb1 = get_embedding(text1)
        emb2 = get_embedding(text2)
    except Exception as e:
        print(f"Error getting embeddings: {e}")
        return

    similarity = cosine_similarity(emb1, emb2)
    confidence = round(similarity * 100, 2)

    print(f"\nMatch Confidence: {confidence}%")

if __name__ == "__main__":
    main()
