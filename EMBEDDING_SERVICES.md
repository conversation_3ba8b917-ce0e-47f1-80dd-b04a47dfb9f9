# 🔗 Embedding Services Configuration Guide

Instead of using local Ollama (`http://localhost:11434`), you can use various cloud-based embedding services. Here are the main options:

## 1. 🎯 **Nomic AI Direct API** (Recommended)

**Best for**: High-quality embeddings, good performance, reasonable pricing

### Setup:
```bash
# Get API key from: https://atlas.nomic.ai/
export NOMIC_API_KEY="your_nomic_api_key_here"
export EMBEDDER_TYPE="nomic_api"
```

### .env file:
```env
NOMIC_API_KEY=your_nomic_api_key_here
EMBEDDER_TYPE=nomic_api
EMBEDDING_MODEL=nomic-embed-text-v1.5
```

### Features:
- ✅ High-quality embeddings (768 dimensions)
- ✅ Up to 8192 tokens per request
- ✅ Optimized for search and retrieval
- ✅ Good pricing ($0.02 per 1M tokens)

---

## 2. 🤖 **OpenAI Embeddings**

**Best for**: Proven quality, wide compatibility, enterprise support

### Setup:
```bash
# Get API key from: https://platform.openai.com/api-keys
export OPENAI_API_KEY="your_openai_api_key_here"
export EMBEDDER_TYPE="openai"
```

### .env file:
```env
OPENAI_API_KEY=your_openai_api_key_here
EMBEDDER_TYPE=openai
EMBEDDING_MODEL=text-embedding-3-small
# Alternative: text-embedding-3-large (better quality, higher cost)
```

### Features:
- ✅ Proven quality and reliability
- ✅ Multiple model sizes available
- ✅ 1536 dimensions (3-small) or 3072 (3-large)
- ⚠️ Higher cost ($0.02-$0.13 per 1M tokens)

---

## 3. 🤗 **Hugging Face Inference API**

**Best for**: Free tier available, many model options

### Setup:
```bash
# Get API key from: https://huggingface.co/settings/tokens
export HUGGINGFACE_API_KEY="your_hf_token_here"
export EMBEDDER_TYPE="huggingface"
```

### .env file:
```env
HUGGINGFACE_API_KEY=your_hf_token_here
EMBEDDER_TYPE=huggingface
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
# Alternative: nomic-ai/nomic-embed-text-v1.5
```

### Features:
- ✅ Free tier available
- ✅ Many model options
- ✅ Good for experimentation
- ⚠️ Rate limits on free tier

---

## 4. 🚀 **Together AI**

**Best for**: Fast inference, competitive pricing

### Setup:
```bash
# Get API key from: https://api.together.xyz/settings/api-keys
export TOGETHER_API_KEY="your_together_api_key_here"
export EMBEDDER_TYPE="together"
```

### .env file:
```env
TOGETHER_API_KEY=your_together_api_key_here
EMBEDDER_TYPE=together
EMBEDDING_MODEL=togethercomputer/m2-bert-80M-8k-retrieval
```

### Features:
- ✅ Fast inference
- ✅ Competitive pricing
- ✅ Good for high-volume usage
- ✅ Multiple embedding models

---

## 5. ⚡ **Groq** (Same as your LLM provider)

**Best for**: Using same provider for both LLM and embeddings

### Setup:
```bash
# Use your existing Groq API key
export GROQ_API_KEY="your_groq_api_key_here"
export EMBEDDER_TYPE="groq"
```

### .env file:
```env
GROQ_API_KEY=your_groq_api_key_here
EMBEDDER_TYPE=groq
EMBEDDING_MODEL=nomic-embed-text-v1.5
```

---

## 🔧 **How to Switch Embedding Services**

### Method 1: Environment Variables
```bash
# For Nomic AI
export EMBEDDER_TYPE="nomic_api"
export NOMIC_API_KEY="your_key"

# For OpenAI
export EMBEDDER_TYPE="openai" 
export OPENAI_API_KEY="your_key"

# For Hugging Face
export EMBEDDER_TYPE="huggingface"
export HUGGINGFACE_API_KEY="your_key"
```

### Method 2: Update .env file
```env
# Choose one:
EMBEDDER_TYPE=nomic_api     # Nomic AI Direct
EMBEDDER_TYPE=openai        # OpenAI
EMBEDDER_TYPE=huggingface   # Hugging Face
EMBEDDER_TYPE=together      # Together AI
EMBEDDER_TYPE=ollama        # Local Ollama (default)
```

### Method 3: Code Configuration
```python
# In your code
pipeline = PDFEmbeddingPipeline(embedder_type="nomic_api")
search_system = MedicalSearchSystem(embedder_type="nomic_api")
```

---

## 💰 **Cost Comparison** (per 1M tokens)

| Service | Model | Cost | Quality | Speed |
|---------|-------|------|---------|-------|
| Nomic AI | nomic-embed-text-v1.5 | $0.02 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| OpenAI | text-embedding-3-small | $0.02 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| OpenAI | text-embedding-3-large | $0.13 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| Hugging Face | Free tier | $0.00 | ⭐⭐⭐ | ⭐⭐ |
| Together AI | m2-bert-80M | $0.01 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Ollama | Local | $0.00 | ⭐⭐⭐ | ⭐⭐⭐ |

---

## 🎯 **Recommendations**

### For Production:
1. **Nomic AI** - Best balance of quality, speed, and cost
2. **OpenAI** - Most reliable, enterprise-ready
3. **Together AI** - Best for high-volume usage

### For Development:
1. **Hugging Face** - Free tier for testing
2. **Ollama** - Local development, no API costs
3. **Nomic AI** - Good free tier for small projects

### For Medical Documents:
1. **Nomic AI** - Optimized for retrieval tasks
2. **OpenAI text-embedding-3-large** - Highest quality
3. **Together AI** - Good performance for domain-specific content

---

## 🚀 **Quick Start**

1. **Choose a service** from the options above
2. **Get an API key** from the provider
3. **Update your .env file** with the credentials
4. **Restart your application**

```bash
# Example for Nomic AI
echo "EMBEDDER_TYPE=nomic_api" >> .env
echo "NOMIC_API_KEY=your_key_here" >> .env
python flask_app.py
```

The system will automatically use the new embedding service!
