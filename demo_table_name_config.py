#!/usr/bin/env python3
"""
Demonstration of dynamic table name configuration
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def demo_table_name_configuration():
    """Demonstrate how to use different table names"""
    
    print("🎯 DYNAMIC TABLE NAME CONFIGURATION DEMO")
    print("=" * 60)
    
    print("📋 Current Configuration:")
    print(f"   DB_NAME: {os.getenv('DB_NAME')}")
    print(f"   DB_TABLE_NAME: {os.getenv('DB_TABLE_NAME')}")
    
    # Test with current configuration
    print(f"\n🔍 Testing with current table name...")
    test_table_access(os.getenv('DB_TABLE_NAME', 'documents'))
    
    # Show how to change table name
    print(f"\n💡 How to Change Table Name:")
    print(f"   1. Edit .env file:")
    print(f"      DB_TABLE_NAME=your_table_name")
    print(f"   2. Restart the application")
    
    # Show available tables
    print(f"\n📊 Available Tables in Database:")
    show_available_tables()
    
    # Test search system with current table
    print(f"\n🔍 Testing Search System:")
    test_search_system()

def test_table_access(table_name):
    """Test access to a specific table"""
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            cur.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cur.fetchone()[0]
            
            cur.execute(f"SELECT COUNT(DISTINCT filename) FROM {table_name}")
            files = cur.fetchone()[0]
            
            print(f"   ✅ Table '{table_name}': {count:,} records, {files:,} files")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error accessing '{table_name}': {e}")

def show_available_tables():
    """Show all available tables in the database"""
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            cur.execute("""
                SELECT table_name, 
                       CASE 
                           WHEN table_name LIKE '%document%' THEN 'Document Table'
                           ELSE 'Other Table'
                       END as table_type
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                  AND table_name LIKE '%document%'
                ORDER BY table_name
            """)
            
            tables = cur.fetchall()
            
            for table_name, table_type in tables:
                # Get record count for document tables
                try:
                    cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cur.fetchone()[0]
                    print(f"   📄 {table_name}: {count:,} records")
                except:
                    print(f"   📄 {table_name}: (access error)")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error listing tables: {e}")

def test_search_system():
    """Test the search system with current configuration"""
    
    try:
        from search_system import VectorSearchEngine
        
        search_engine = VectorSearchEngine()
        table_name = search_engine.db_manager.table_name
        
        print(f"   📋 Search engine using table: {table_name}")
        
        # Test search
        results = search_engine.search_similar_documents("payment", top_k=2)
        print(f"   🔍 Search test: {len(results)} results found")
        
        if results:
            print(f"   📄 Sample result: {results[0].get('filename')}")
        
    except Exception as e:
        print(f"   ❌ Search system error: {e}")

def demo_changing_table_name():
    """Demonstrate how to change table name"""
    
    print(f"\n🔄 CHANGING TABLE NAME DEMO")
    print("=" * 40)
    
    # Show current setting
    current_table = os.getenv('DB_TABLE_NAME', 'documents')
    print(f"📋 Current table: {current_table}")
    
    # Test different table names
    test_tables = ['documents', 'documents_enhanced', 'data_documents']
    
    for table in test_tables:
        print(f"\n🧪 Testing table: {table}")
        
        # Temporarily set environment variable
        os.environ['DB_TABLE_NAME'] = table
        
        try:
            from search_system import VectorSearchEngine
            
            # Create new search engine with updated table name
            search_engine = VectorSearchEngine()
            actual_table = search_engine.db_manager.table_name
            
            print(f"   📋 Search engine table: {actual_table}")
            
            # Test search
            results = search_engine.search_similar_documents("test", top_k=1)
            print(f"   ✅ Search successful: {len(results)} results")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:50]}...")
    
    # Restore original setting
    os.environ['DB_TABLE_NAME'] = current_table

def show_configuration_examples():
    """Show configuration examples"""
    
    print(f"\n📝 CONFIGURATION EXAMPLES")
    print("=" * 40)
    
    examples = [
        ("Default Documents", "documents"),
        ("Enhanced Documents", "documents_enhanced"),
        ("Data Documents", "data_documents"),
        ("Custom Table", "my_custom_documents")
    ]
    
    print(f"💡 To use different tables, update your .env file:")
    print()
    
    for name, table in examples:
        print(f"   # {name}")
        print(f"   DB_TABLE_NAME={table}")
        print()
    
    print(f"🔄 After changing .env, restart your application:")
    print(f"   python3 flask_app.py")
    print(f"   python3 main_app.py")

def main():
    """Main demonstration"""
    
    demo_table_name_configuration()
    demo_changing_table_name()
    show_configuration_examples()
    
    print(f"\n🎉 SUMMARY")
    print("=" * 20)
    print(f"✅ Table name configuration is working correctly!")
    print(f"✅ Current table: {os.getenv('DB_TABLE_NAME', 'documents')}")
    print(f"✅ Search system uses dynamic table names")
    print(f"✅ Easy to switch between different document tables")
    
    print(f"\n📚 Usage:")
    print(f"   1. Set DB_TABLE_NAME in .env file")
    print(f"   2. Restart your application")
    print(f"   3. All components will use the new table")

if __name__ == "__main__":
    main()
