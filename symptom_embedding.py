"""
Modified PDF Embedding System for Combined Symptoms Storage
Stores all symptoms of a diagnosis as one combined text with single embedding
"""

import os
import fitz  # PyMuPDF
import psycopg2
import requests
import logging
from typing import List, Dict
from dotenv import load_dotenv
from pathlib import Path
import re
import uuid

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('symptom_embedder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NomicEmbedder:
    """Embedder using nomic-ai/nomic-embed-text-v1 via Nomic AI API or Ollama"""

    def __init__(self, api_key: str = None, use_ollama: bool = True, ollama_base_url: str = "http://localhost:11434"):
        self.use_ollama = use_ollama

        if use_ollama:
            self.base_url = ollama_base_url
            self.model_name = "nomic-embed-text:latest"
            self.api_key = None
            logger.info(f"Initialized NomicEmbedder with Ollama - base_url: {self.base_url}")
        else:
            self.api_key = api_key or os.getenv("NOMIC_API_KEY")
            self.base_url = "https://api-atlas.nomic.ai/v1"
            self.model_name = "nomic-embed-text-v1.5"

            if not self.api_key:
                logger.warning("No NOMIC_API_KEY found. Falling back to Ollama.")
                self.use_ollama = True
                self.base_url = ollama_base_url
                self.model_name = "nomic-embed-text:latest"
                self.api_key = None

            logger.info(f"Initialized NomicEmbedder with Nomic API - model: {self.model_name}")
        
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a list of texts using Nomic API or Ollama"""
        logger.info(f"Generating embeddings for {len(texts)} texts using {'Ollama' if self.use_ollama else 'Nomic API'}")
        embeddings = []

        for i, text in enumerate(texts):
            try:
                # Truncate very long texts to avoid issues
                max_length = 8000 if self.use_ollama else 8192
                if len(text) > max_length:
                    text = text[:max_length]

                if self.use_ollama:
                    response = requests.post(
                        f"{self.base_url}/api/embeddings",
                        json={
                            "model": self.model_name,
                            "prompt": text
                        },
                        timeout=30
                    )
                else:
                    response = requests.post(
                        f"{self.base_url}/embeddings",
                        headers={
                            "Authorization": f"Bearer {self.api_key}",
                            "Content-Type": "application/json"
                        },
                        json={
                            "model": self.model_name,
                            "texts": [text],
                            "task_type": "search_document"
                        },
                        timeout=30
                    )

                if response.status_code == 200:
                    result = response.json()

                    if self.use_ollama:
                        embedding = result.get("embedding")
                    else:
                        embeddings_data = result.get("embeddings", [])
                        embedding = embeddings_data[0] if embeddings_data else None

                    if embedding and len(embedding) > 0:
                        embeddings.append(embedding)
                    else:
                        logger.warning(f"Empty embedding returned for text {i}")
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                        embeddings.append(random_embedding)
                else:
                    logger.error(f"Error getting embedding for text {i}: {response.status_code}")
                    import random
                    random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                    embeddings.append(random_embedding)

            except Exception as e:
                logger.error(f"Error processing text {i}: {e}")
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                embeddings.append(random_embedding)

        return embeddings

class MedicalDocumentParser:
    """Parse medical documents and extract structured information"""

    def __init__(self):
        self.symptom_patterns = [
            r'(?i)^[-•*]\s*(.+)$',  # Bullet points
            r'(?i)^(\d+[\.)]\s*(.+))$',  # Numbered lists
            r'(?i)(.+?)(?:\s*[-–]\s*(.+))?$'  # General symptom format
        ]

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF file"""
        logger.info(f"Extracting text from PDF: {pdf_path}")
        try:
            doc = fitz.open(pdf_path)
            text = ""

            for page_num in range(doc.page_count):
                page = doc[page_num]
                page_text = page.get_text()
                text += page_text

            doc.close()
            extracted_text = text.strip()
            logger.info(f"Successfully extracted {len(extracted_text)} characters from {pdf_path}")
            return extracted_text

        except Exception as e:
            logger.error(f"Error extracting text from {pdf_path}: {e}")
            return ""

    def parse_medical_document(self, text: str, filename: str) -> Dict:
        """Parse medical document and extract structured information"""
        logger.info(f"Parsing medical document: {filename}")
        
        # Extract diagnosis name from filename or content
        diagnosis_name = self._extract_diagnosis_name(text, filename)
        
        # Extract symptoms
        symptoms = self._extract_symptoms(text)
        
        # Extract treatment information
        treatment = self._extract_treatment(text)
        
        # Extract description/overview
        description = self._extract_description(text)
        
        # Extract additional notes
        notes = self._extract_notes(text)

        parsed_data = {
            "diagnosis_name": diagnosis_name,
            "description": description,
            "symptoms": symptoms,
            "treatment": treatment,
            "notes": notes,
            "filename": filename
        }
        
        logger.info(f"Parsed document with {len(symptoms)} symptoms for {diagnosis_name}")
        return parsed_data

    def _extract_diagnosis_name(self, text: str, filename: str) -> str:
        """Extract diagnosis name from filename or document title"""
        # First try to get from filename
        diagnosis_from_filename = Path(filename).stem.replace('_', ' ').replace('-', ' ').title()
        
        # Try to find title in document (look for # headings or first bold text)
        lines = text.split('\n')
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if line.startswith('#'):
                title = line.replace('#', '').strip()
                if title and len(title) < 100:
                    return title
            elif line.isupper() and len(line) > 3 and len(line) < 50:
                return line.title()
        
        return diagnosis_from_filename

    def _extract_symptoms(self, text: str) -> str:
        """Extract all symptoms as combined text"""
        symptom_lines = []
        lines = text.split('\n')

        in_symptoms_section = False

        for line in lines:
            line = line.strip()

            # Check if we're entering a symptoms section
            if re.match(r'(?i)^(symptoms?|signs?|clinical\s+signs?):?\s*$', line):
                in_symptoms_section = True
                continue

            # Check if we're leaving symptoms section
            if in_symptoms_section and re.match(r'(?i)^(treatment|therapy|diagnosis|overview|occurrence):?\s*$', line):
                in_symptoms_section = False
                continue

            # Extract symptoms if we're in the symptoms section
            if in_symptoms_section and line:
                # Skip section headers
                if ':' in line and len(line) < 50:
                    continue

                # Extract symptom from various formats
                symptom = self._clean_symptom_text(line)
                if symptom and len(symptom) > 5:  # Filter out very short entries
                    symptom_lines.append(symptom)

        # If no symptoms section found, try to extract from bullet points anywhere
        if not symptom_lines:
            symptom_lines = self._extract_symptoms_from_bullets(text)

        # Combine all symptoms into one text block
        return '\n'.join(symptom_lines)

    def _extract_symptoms_from_bullets(self, text: str) -> List[str]:
        """Extract symptoms from bullet points throughout the document"""
        symptoms = []
        lines = text.split('\n')

        for line in lines:
            line = line.strip()

            # Match bullet points
            bullet_match = re.match(r'^[-•*]\s*(.+)$', line)
            if bullet_match:
                symptom = self._clean_symptom_text(bullet_match.group(1))
                if symptom and len(symptom) > 5:
                    symptoms.append(symptom)

        return symptoms

    def _clean_symptom_text(self, text: str) -> str:
        """Clean and normalize symptom text"""
        # Remove bullet points and numbering
        text = re.sub(r'^[-•*\d\.)]\s*', '', text)
        
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        # Remove trailing punctuation
        text = text.rstrip('.,;:')
        
        return text.strip()

    def _extract_treatment(self, text: str) -> str:
        """Extract treatment information"""
        lines = text.split('\n')
        treatment_lines = []
        in_treatment_section = False
        
        for line in lines:
            line = line.strip()
            
            if re.match(r'(?i)^(treatment|therapy):?\s*$', line):
                in_treatment_section = True
                continue
            
            if in_treatment_section and re.match(r'(?i)^(symptoms?|diagnosis|overview|occurrence):?\s*$', line):
                in_treatment_section = False
                continue
            
            if in_treatment_section and line:
                treatment_lines.append(line)
        
        return '\n'.join(treatment_lines).strip()

    def _extract_description(self, text: str) -> str:
        """Extract description/overview"""
        lines = text.split('\n')
        description_lines = []
        in_description_section = False
        
        for line in lines:
            line = line.strip()
            
            if re.match(r'(?i)^(overview|description|occurrence):?\s*$', line):
                in_description_section = True
                continue
            
            if in_description_section and re.match(r'(?i)^(symptoms?|treatment|diagnosis):?\s*$', line):
                in_description_section = False
                continue
            
            if in_description_section and line:
                description_lines.append(line)
        
        return '\n'.join(description_lines).strip()

    def _extract_notes(self, text: str) -> str:
        """Extract additional notes"""
        lines = text.split('\n')
        notes_lines = []
        
        # Look for note patterns
        for line in lines:
            line = line.strip()
            if re.match(r'(?i)^note:?\s*(.+)', line):
                notes_lines.append(line)
        
        return '\n'.join(notes_lines).strip()

class PostgreSQLManager:
    """Manage PostgreSQL database operations for diagnoses table"""

    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")

        if self.database_url:
            self.db_config = None
            logger.info("Using DATABASE_URL for database connection")
        else:
            self.db_config = {
                "dbname": os.getenv("DB_NAME", "kforce"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASS", "kforce"),
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5444")
            }
            logger.info(f"Using individual DB config: {self.db_config['host']}:{self.db_config['port']}")

    def get_connection(self):
        """Get database connection with retry logic"""
        import time
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                if self.database_url:
                    conn = psycopg2.connect(self.database_url)
                else:
                    conn = psycopg2.connect(**self.db_config)

                # Test the connection
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                    cur.fetchone()

                logger.info(f"Database connection established on attempt {attempt + 1}")
                return conn

            except Exception as e:
                logger.warning(f"Connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    raise

    def create_diagnoses_table(self):
        """Create diagnoses table as specified"""
        logger.info("Creating diagnoses table...")

        create_table_sql = """
        -- Enable pgvector extension
        CREATE EXTENSION IF NOT EXISTS vector;

        -- Create diagnoses table
        CREATE TABLE IF NOT EXISTS diagnoses (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL,
            description TEXT,
            symptoms TEXT NOT NULL,
            treatment TEXT,
            notes TEXT,
            embedding vector(1536)
        );

        -- Create index for vector similarity search
        CREATE INDEX IF NOT EXISTS diagnoses_embedding_idx
        ON diagnoses USING ivfflat (embedding vector_cosine_ops)
        WITH (lists = 100);

        -- Create index for name searches
        CREATE INDEX IF NOT EXISTS diagnoses_name_idx ON diagnoses(name);
        """

        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(create_table_sql)
                    conn.commit()
                    logger.info("Diagnoses table created successfully!")

        except Exception as e:
            logger.error(f"Error creating table: {e}")

    def insert_diagnosis_record(self, diagnosis_data: Dict, embedding: List[float]):
        """Insert single diagnosis record with combined symptoms into diagnoses table"""
        symptoms = diagnosis_data["symptoms"]

        if not embedding or len(embedding) == 0:
            logger.warning("Empty embedding provided, using random embedding")
            import random
            embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]

        logger.info(f"Inserting diagnosis record for {diagnosis_data['diagnosis_name']}")

        insert_sql = """
        INSERT INTO diagnoses (id, name, description, symptoms, treatment, notes, embedding)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (id) DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            symptoms = EXCLUDED.symptoms,
            treatment = EXCLUDED.treatment,
            notes = EXCLUDED.notes,
            embedding = EXCLUDED.embedding
        """

        # Generate UUID for the diagnosis record
        diagnosis_id = str(uuid.uuid4())

        data = (
            diagnosis_id,
            diagnosis_data["diagnosis_name"],
            diagnosis_data["description"],
            symptoms,  # Combined symptoms as TEXT
            diagnosis_data["treatment"],
            diagnosis_data["notes"],
            embedding
        )

        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(insert_sql, data)
                    conn.commit()
                    logger.info(f"Successfully inserted diagnosis record for {diagnosis_data['diagnosis_name']}")

        except Exception as e:
            logger.error(f"Error inserting record: {e}")

    def get_record_count(self) -> int:
        """Get total number of records in diagnoses table"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT COUNT(*) FROM diagnoses")
                    return cur.fetchone()[0]
        except Exception as e:
            logger.error(f"Error getting record count: {e}")
            return 0

class SymptomEmbeddingPipeline:
    """Main pipeline for processing medical documents and storing combined symptoms"""

    def __init__(self, embedder_type: str = "ollama"):
        logger.info(f"Initializing SymptomEmbeddingPipeline with embedder: {embedder_type}")

        if embedder_type.lower() == "ollama":
            self.embedder = NomicEmbedder(use_ollama=True)
        else:
            logger.warning(f"Unknown embedder type: {embedder_type}, defaulting to Ollama")
            self.embedder = NomicEmbedder(use_ollama=True)

        self.parser = MedicalDocumentParser()
        self.db_manager = PostgreSQLManager()
        logger.info("SymptomEmbeddingPipeline initialized successfully")

    def process_pdf_folder(self, folder_path: str):
        """Process all PDFs in a folder"""
        logger.info(f"Processing PDF folder: {folder_path}")
        folder_path = Path(folder_path)

        if not folder_path.exists():
            logger.error(f"Folder {folder_path} does not exist")
            return

        # Create database table
        self.db_manager.create_diagnoses_table()

        # Get all PDF files
        pdf_files = list(folder_path.glob("*.pdf"))
        logger.info(f"Found {len(pdf_files)} PDF files")

        for i, pdf_file in enumerate(pdf_files, 1):
            logger.info(f"Processing file {i}/{len(pdf_files)}: {pdf_file.name}")
            self.process_single_pdf(str(pdf_file))

        total_records = self.db_manager.get_record_count()
        logger.info(f"Processing complete! Total records in database: {total_records}")

    def process_single_pdf(self, pdf_path: str):
        """Process a single PDF file and store combined symptoms as one record"""
        filename = Path(pdf_path).name
        logger.info(f"Processing PDF: {filename}")

        # Extract text
        text = self.parser.extract_text_from_pdf(pdf_path)
        if not text:
            logger.warning(f"No text extracted from {filename}")
            return

        # Parse medical document
        diagnosis_data = self.parser.parse_medical_document(text, filename)

        if not diagnosis_data["symptoms"]:
            logger.warning(f"No symptoms found in {filename}")
            return

        logger.info(f"Found combined symptoms text for {filename}")

        # Generate single embedding for combined symptoms
        combined_symptoms = diagnosis_data["symptoms"]
        embeddings = self.embedder.get_embeddings([combined_symptoms])

        if embeddings and len(embeddings) > 0:
            embedding = embeddings[0]
        else:
            logger.warning(f"Failed to generate embedding for {filename}")
            return

        # Store in database - one row per diagnosis with combined symptoms
        self.db_manager.insert_diagnosis_record(diagnosis_data, embedding)
        logger.info(f"Successfully processed combined symptoms from {filename}")

if __name__ == "__main__":
    # Initialize pipeline
    pipeline = SymptomEmbeddingPipeline(embedder_type="ollama")
    
    # Process pdf folder
    pdf_folder = "diseases"
    pipeline.process_pdf_folder(pdf_folder)