#!/usr/bin/env python3
"""
Debug script to test similarity score calculation
"""

import os
import psycopg2
from dotenv import load_dotenv
from search_system import MedicalSearchSystem, VectorSearchEngine
from pdf_embedder import NomicEmbedder

# Load environment variables
load_dotenv()

def test_database_connection():
    """Test database connection and check for data"""
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            table_name = os.getenv("DB_TABLE_NAME", "documents")
            
            # Check if table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                );
            """, (table_name,))
            
            table_exists = cur.fetchone()[0]
            print(f"Table '{table_name}' exists: {table_exists}")
            
            if table_exists:
                # Check total records
                cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                total_records = cur.fetchone()[0]
                print(f"Total records: {total_records}")
                
                # Check for null embeddings
                cur.execute(f"SELECT COUNT(*) FROM {table_name} WHERE embedding IS NULL")
                null_embeddings = cur.fetchone()[0]
                print(f"Records with null embeddings: {null_embeddings}")
                
                # Check embedding dimensions
                cur.execute(f"SELECT array_length(embedding, 1) as dim FROM {table_name} WHERE embedding IS NOT NULL LIMIT 5")
                dimensions = cur.fetchall()
                print(f"Embedding dimensions (first 5): {[d[0] for d in dimensions]}")
                
                # Test a simple similarity query
                cur.execute(f"""
                    SELECT filename, 
                           embedding IS NOT NULL as has_embedding,
                           array_length(embedding, 1) as dim
                    FROM {table_name} 
                    LIMIT 3
                """)
                
                sample_records = cur.fetchall()
                print("\nSample records:")
                for record in sample_records:
                    print(f"  File: {record[0]}, Has embedding: {record[1]}, Dimension: {record[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Database connection error: {e}")
        return False

def test_embedder():
    """Test the embedder directly"""
    print("\n" + "="*50)
    print("Testing Embedder")
    print("="*50)
    
    embedder = NomicEmbedder()
    test_query = "What are the symptoms of acidosis?"
    
    print(f"Testing query: '{test_query}'")
    embeddings = embedder.get_embeddings([test_query])
    
    if embeddings:
        embedding = embeddings[0]
        print(f"Generated embedding dimension: {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
        print(f"All zeros: {all(x == 0.0 for x in embedding)}")
        
        # Check for NaN values
        import math
        nan_count = sum(1 for x in embedding if math.isnan(x))
        print(f"NaN values in embedding: {nan_count}")
        
        return embedding
    else:
        print("No embeddings generated!")
        return None

def test_similarity_calculation():
    """Test similarity calculation directly"""
    print("\n" + "="*50)
    print("Testing Similarity Calculation")
    print("="*50)
    
    search_engine = VectorSearchEngine()
    test_query = "What are the symptoms of acidosis?"
    
    print(f"Testing search for: '{test_query}'")
    results = search_engine.search_similar_documents(test_query, top_k=5)
    
    print(f"Found {len(results)} results")
    for i, result in enumerate(results):
        print(f"Result {i+1}:")
        print(f"  File: {result['filename']}")
        print(f"  Similarity: {result['similarity_score']}")
        print(f"  Content preview: {result['content'][:100]}...")
        print()

def test_full_search_system():
    """Test the full search system"""
    print("\n" + "="*50)
    print("Testing Full Search System")
    print("="*50)
    
    search_system = MedicalSearchSystem()
    test_query = "What are the symptoms of acidosis?"
    
    print(f"Testing full search for: '{test_query}'")
    result = search_system.search_and_answer(test_query, top_k=3)
    
    print(f"Documents found: {result['documents_found']}")
    print(f"Answer: {result['answer'][:200]}...")
    
    if result['sources']:
        print("\nSources:")
        for i, source in enumerate(result['sources']):
            print(f"  {i+1}. {source['filename']} - Similarity: {source['similarity_score']}")

def main():
    """Run all tests"""
    print("🔍 Debugging Similarity Score Issues")
    print("="*60)
    
    # Test 1: Database connection and data
    print("\n1. Testing Database Connection...")
    if not test_database_connection():
        print("❌ Database test failed - stopping here")
        return
    
    # Test 2: Embedder
    print("\n2. Testing Embedder...")
    embedding = test_embedder()
    if not embedding:
        print("❌ Embedder test failed - stopping here")
        return
    
    # Test 3: Similarity calculation
    print("\n3. Testing Similarity Calculation...")
    test_similarity_calculation()
    
    # Test 4: Full search system
    print("\n4. Testing Full Search System...")
    test_full_search_system()
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main()
