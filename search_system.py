"""
Search System using LlamaIndex with PostgreSQL Vector Store and Grok LLM
"""

import os
import requests
import json
import logging
from typing import List, Dict, Optional
from dotenv import load_dotenv
import psycopg2
from pdf_embedder import NomicEmbedder, OpenAICompatibleEmbedder, HuggingFaceEmbedder, TogetherAIEmbedder, PostgreSQLManager
import pandas as pd

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('search_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GroqLLM:
    """Groq LLM integration for final response generation"""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
        self.model = "llama-3.3-70b-versatile"  # Using Groq's fast Llama model

        logger.info(f"Initialized GroqLLM with model: {self.model}")
        if not self.api_key:
            logger.warning("No GROQ_API_KEY found in environment variables")

    def generate_response(self, query: str, context: str) -> str:
        """Generate response using Groq LLM"""

        logger.info(f"Generating LLM response for query: '{query[:100]}...'")
        logger.debug(f"Context length: {len(context)} characters")

        prompt = f"""You are an expert medical assistant specializing in animal diseases and veterinary medicine.
Use the provided context to answer the user's question accurately and comprehensively.

Context from medical documents:
{context}

User Question: {query}

Instructions:
- Provide a detailed, accurate answer based solely on the context provided
- If the context doesn't contain enough information, clearly state what information is missing
- Include relevant symptoms, treatments, or diagnostic information when available
- Use medical terminology appropriately but explain complex terms
- Do not recommend treatments without proper veterinary consultation disclaimers

Answer:"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert veterinary medical assistant. Provide accurate, helpful information about animal diseases and health conditions based on the provided context."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "model": self.model,
            "stream": False,
            "temperature": 0.3
        }

        try:
            logger.debug("Sending request to Groq API...")
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                answer = result["choices"][0]["message"]["content"]
                logger.info(f"Successfully generated LLM response ({len(answer)} characters)")
                logger.debug(f"LLM response preview: {answer[:200]}...")
                return answer
            else:
                error_msg = f"Groq API Error: {response.status_code}"
                logger.error(f"{error_msg} - Response: {response.text}")
                return f"Error generating response: {response.status_code}"

        except Exception as e:
            logger.error(f"Error calling Groq API: {e}")
            return f"Error generating response: {str(e)}"

class VectorSearchEngine:
    """Vector search engine using PostgreSQL and embeddings"""

    def __init__(self, embedder_type: str = "nomic"):
        # Choose embedder based on type
        embedder_type_lower = embedder_type.lower()

        if embedder_type_lower == "openai":
            self.embedder = OpenAICompatibleEmbedder()
        elif embedder_type_lower == "nomic_api":
            self.embedder = NomicEmbedder(use_ollama=False)
        elif embedder_type_lower == "huggingface" or embedder_type_lower == "hf":
            self.embedder = HuggingFaceEmbedder()
        elif embedder_type_lower == "together" or embedder_type_lower == "together_ai":
            self.embedder = TogetherAIEmbedder()
        elif embedder_type_lower == "ollama" or embedder_type_lower == "nomic":
            self.embedder = NomicEmbedder(use_ollama=True)
        else:
            logger.warning(f"Unknown embedder type: {embedder_type}, defaulting to Nomic with Ollama")
            self.embedder = NomicEmbedder(use_ollama=True)

        self.db_manager = PostgreSQLManager()
        logger.info(f"Initialized VectorSearchEngine with {embedder_type} embedder and PostgreSQLManager")
        
    def search_similar_documents(self, query: str, top_k: int = 10) -> List[Dict]:
        """Search for similar documents using vector similarity"""

        logger.info(f"Starting vector search for query: '{query[:100]}...' (top_k={top_k})")

        # Get query embedding
        logger.debug("Generating query embedding...")
        query_embeddings = self.embedder.get_embeddings([query])
        if not query_embeddings:
            logger.warning("No query embeddings generated")
            return []

        query_embedding = query_embeddings[0]

        # Validate query embedding
        if not query_embedding or len(query_embedding) == 0:
            logger.warning("Query embedding is empty")
            return []

        logger.debug(f"Query embedding dimension: {len(query_embedding)}")

        # Check for NaN values in query embedding
        import math
        nan_count = sum(1 for x in query_embedding if math.isnan(x))
        if nan_count > 0:
            logger.warning(f"Query embedding contains {nan_count} NaN values")
            return []
        
        # Search in database
        search_sql = f"""
        SELECT
            chunk_id,
            filename,
            content,
            heading,
            word_count,
            CASE
                WHEN embedding IS NULL THEN 0.0
                WHEN %s::vector IS NULL THEN 0.0
                ELSE GREATEST(0.0, LEAST(1.0, 1 - (embedding <=> %s::vector)))
            END as similarity_score
        FROM {self.db_manager.table_name}
        WHERE embedding IS NOT NULL
        ORDER BY embedding <=> %s::vector
        LIMIT %s
        """
        
        try:
            logger.debug("Executing vector similarity search in database...")
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(search_sql, (query_embedding, query_embedding, query_embedding, top_k))
                    results = cur.fetchall()

                    logger.info(f"Found {len(results)} raw results from database")

                    # Format results
                    documents = []
                    for i, row in enumerate(results):
                        # Handle similarity score more robustly
                        similarity_score = row[5]
                        original_score = similarity_score  # For logging

                        if similarity_score is not None:
                            try:
                                # Convert to float and check if it's a valid number
                                similarity_score = float(similarity_score)
                                if pd.isna(similarity_score) or similarity_score < 0 or similarity_score > 1:
                                    logger.warning(f"Invalid similarity score {original_score} for row {i}, setting to 0.0")
                                    similarity_score = 0.0
                            except (ValueError, TypeError) as e:
                                logger.warning(f"Error converting similarity score {original_score} for row {i}: {e}")
                                similarity_score = 0.0
                        else:
                            logger.debug(f"Null similarity score for row {i}")
                            similarity_score = 0.0

                        if i < 3:  # Log first few results for debugging
                            logger.debug(f"Row {i} - File: {row[1][:30]}..., Original score: {original_score}, Final score: {similarity_score}")

                        documents.append({
                            "chunk_id": row[0],
                            "filename": row[1],
                            "content": row[2],
                            "heading": row[3],
                            "word_count": row[4],
                            "similarity_score": similarity_score
                        })

                    logger.info(f"Successfully processed {len(documents)} documents from search results")
                    if documents:
                        avg_score = sum(doc['similarity_score'] for doc in documents) / len(documents)
                        logger.debug(f"Average similarity score: {avg_score:.4f}")

                    return documents

        except Exception as e:
            logger.error(f"Error searching documents: {e}", exc_info=True)
            return []
    
    def search_by_filename(self, filename_pattern: str, top_k: int = 5) -> List[Dict]:
        """Search documents by filename pattern"""
        
        search_sql = f"""
        SELECT
            chunk_id,
            filename,
            content,
            word_count
        FROM {self.db_manager.table_name}
        WHERE filename ILIKE %s
        ORDER BY chunk_index
        LIMIT %s
        """
        
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(search_sql, (f"%{filename_pattern}%", top_k))
                    results = cur.fetchall()
                    
                    documents = []
                    for row in results:
                        documents.append({
                            "chunk_id": row[0],
                            "filename": row[1],
                            "content": row[2],
                            "word_count": row[3],
                            "similarity_score": 1.0  # Perfect match for filename search
                        })
                    
                    return documents
                    
        except Exception as e:
            print(f"Error searching by filename: {e}")
            return []

class MedicalSearchSystem:
    """Complete medical search system combining vector search and LLM"""

    def __init__(self, embedder_type: str = "nomic"):
        logger.info(f"Initializing MedicalSearchSystem with embedder type: {embedder_type}")
        self.search_engine = VectorSearchEngine(embedder_type=embedder_type)
        self.llm = GroqLLM()
        logger.info("MedicalSearchSystem initialized successfully")
        
    def search_and_answer(self, query: str, top_k: int = 10) -> Dict:
        """Search for relevant documents and generate answer"""

        logger.info(f"Starting search and answer for query: '{query[:100]}...' (top_k={top_k})")

        # Search for similar documents
        documents = self.search_engine.search_similar_documents(query, top_k)

        if not documents:
            logger.warning("No relevant documents found for query")
            return {
                "query": query,
                "documents_found": 0,
                "answer": "No relevant documents found for your query.",
                "sources": []
            }

        logger.info(f"Found {len(documents)} relevant documents")
        
        # Prepare context from retrieved documents
        logger.debug("Preparing context from retrieved documents...")
        context_parts = []
        sources = []

        for i, doc in enumerate(documents, 1):
            heading = doc.get('heading', 'Unknown Section')
            context_parts.append(f"Document {i} ({doc['filename']} - {heading}):\n{doc['content']}")
            sources.append({
                "filename": doc["filename"],
                "heading": heading,
                "similarity_score": doc["similarity_score"],
                "chunk_id": doc["chunk_id"]
            })

        context = "\n\n---\n\n".join(context_parts)
        logger.debug(f"Prepared context with {len(context)} characters from {len(sources)} sources")

        # Log source information
        for i, source in enumerate(sources[:3]):  # Log first 3 sources
            logger.debug(f"Source {i+1}: {source['filename']} (score: {source['similarity_score']:.4f})")

        # Generate answer using Groq LLM
        logger.info("Generating response with Groq LLM...")
        answer = self.llm.generate_response(query, context)
        
        result = {
            "query": query,
            "documents_found": len(documents),
            "answer": answer,
            "sources": sources,
            "context": context
        }

        logger.info(f"Search and answer completed successfully - found {len(documents)} documents, generated {len(answer)} character response")
        return result

    def get_database_stats(self) -> Dict:
        """Get database statistics"""
        logger.debug("Getting database statistics...")
        try:
            with self.search_engine.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    table_name = self.search_engine.db_manager.table_name

                    # Get total documents
                    cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                    total_docs = cur.fetchone()[0]

                    # Get unique files
                    cur.execute(f"SELECT COUNT(DISTINCT filename) FROM {table_name}")
                    unique_files = cur.fetchone()[0]

                    # Get file list
                    cur.execute(f"SELECT filename, COUNT(*) as chunks FROM {table_name} GROUP BY filename ORDER BY filename")
                    files = cur.fetchall()

                    stats = {
                        "total_documents": total_docs,
                        "unique_files": unique_files,
                        "files": [{"filename": f[0], "chunks": f[1]} for f in files]
                    }

                    logger.info(f"Database stats: {total_docs} documents from {unique_files} files")
                    return stats

        except Exception as e:
            logger.error(f"Error getting database stats: {e}", exc_info=True)
            return {"error": str(e)}

def main():
    """Main interactive search interface"""
    
    search_system = MedicalSearchSystem()
    
    print("🏥 Medical Document Search System")
    print("=" * 50)
    
    # Show database stats
    stats = search_system.get_database_stats()
    if "error" not in stats:
        print(f"📊 Database contains {stats['total_documents']} document chunks from {stats['unique_files']} files")
        print()
    
    while True:
        try:
            # Get user query
            query = input("\n🔍 Enter your medical question (or 'quit' to exit): ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not query:
                continue
                
            # Search and get answer
            result = search_system.search_and_answer(query)
            
            # Display results
            print(f"\n📋 Found {result['documents_found']} relevant documents")
            print("\n🤖 Answer:")
            print("-" * 30)
            print(result['answer'])
            
            # Show sources
            if result['sources']:
                print(f"\n📚 Sources:")
                for i, source in enumerate(result['sources'][:5], 1):
                    print(f"{i}. {source['filename']} (similarity: {source['similarity_score']:.3f})")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
